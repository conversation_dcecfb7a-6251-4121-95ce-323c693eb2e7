// 聊天记录持久化服务 - 基于IndexedDB的单例模式实现

import { 
  ChatSession, 
  ChatMessage, 
  ChatStorageInterface,
  generateId,
  truncateText 
} from '@/types/chat';

/**
 * 聊天存储服务类 - 单例模式
 */
export class ChatStorage implements ChatStorageInterface {
  private static instance: ChatStorage;
  private static readonly DB_NAME = 'ChatSystemDB';
  private static readonly DB_VERSION = 1;
  private static readonly STORE_NAME = 'sessions';
  
  private db: IDBDatabase | null = null;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): ChatStorage {
    if (!ChatStorage.instance) {
      ChatStorage.instance = new ChatStorage();
    }
    return ChatStorage.instance;
  }

  /**
   * 初始化IndexedDB连接
   */
  private async initDB(): Promise<IDBDatabase> {
    if (this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(ChatStorage.DB_NAME, ChatStorage.DB_VERSION);

      request.onerror = () => {
        reject(new Error(`IndexedDB打开失败: ${request.error?.message}`));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // 创建会话存储对象仓库
        if (!db.objectStoreNames.contains(ChatStorage.STORE_NAME)) {
          const store = db.createObjectStore(ChatStorage.STORE_NAME, {
            keyPath: 'id'
          });
          
          // 创建索引
          store.createIndex('createdAt', 'createdAt', { unique: false });
          store.createIndex('updatedAt', 'updatedAt', { unique: false });
          store.createIndex('module', 'module', { unique: false });
        }
      };
    });
  }

  /**
   * 创建新的空会话
   */
  public async createSession(): Promise<ChatSession> {
    try {
      const db = await this.initDB();
      const now = new Date().toISOString();
      
      const newSession: ChatSession = {
        id: generateId(),
        title: '新对话',
        messages: [],
        createdAt: now,
        updatedAt: now,
        module: 'coding'
      };

      const transaction = db.transaction([ChatStorage.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(ChatStorage.STORE_NAME);
      
      await new Promise<void>((resolve, reject) => {
        const request = store.add(newSession);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error(`创建会话失败: ${request.error?.message}`));
      });

      return newSession;
    } catch (error) {
      console.error('创建会话失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有会话列表
   */
  public async getAllSessions(): Promise<ChatSession[]> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction([ChatStorage.STORE_NAME], 'readonly');
      const store = transaction.objectStore(ChatStorage.STORE_NAME);
      const index = store.index('updatedAt');

      return new Promise((resolve, reject) => {
        const request = index.openCursor(null, 'prev'); // 按更新时间倒序
        const sessions: ChatSession[] = [];

        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            sessions.push(cursor.value);
            cursor.continue();
          } else {
            resolve(sessions);
          }
        };

        request.onerror = () => {
          reject(new Error(`获取会话列表失败: ${request.error?.message}`));
        };
      });
    } catch (error) {
      console.error('获取会话列表失败:', error);
      return [];
    }
  }

  /**
   * 覆盖式更新会话的消息数组
   */
  public async updateSession(sessionId: string, messages: ChatMessage[]): Promise<void> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction([ChatStorage.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(ChatStorage.STORE_NAME);

      // 先获取现有会话
      const getRequest = store.get(sessionId);
      
      await new Promise<void>((resolve, reject) => {
        getRequest.onsuccess = () => {
          const existingSession = getRequest.result as ChatSession;
          if (!existingSession) {
            reject(new Error(`会话不存在: ${sessionId}`));
            return;
          }

          // 更新会话数据
          const updatedSession: ChatSession = {
            ...existingSession,
            messages,
            updatedAt: new Date().toISOString(),
            // 根据第一条用户消息更新标题
            title: messages.length > 0 && messages[0].role === 'user' 
              ? truncateText(messages[0].content, 30)
              : existingSession.title
          };

          // 保存更新后的会话
          const putRequest = store.put(updatedSession);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(new Error(`更新会话失败: ${putRequest.error?.message}`));
        };

        getRequest.onerror = () => {
          reject(new Error(`获取会话失败: ${getRequest.error?.message}`));
        };
      });
    } catch (error) {
      console.error('更新会话失败:', error);
      throw error;
    }
  }

  /**
   * 删除指定会话
   */
  public async deleteSession(sessionId: string): Promise<void> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction([ChatStorage.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(ChatStorage.STORE_NAME);

      await new Promise<void>((resolve, reject) => {
        const request = store.delete(sessionId);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error(`删除会话失败: ${request.error?.message}`));
      });
    } catch (error) {
      console.error('删除会话失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定会话
   */
  public async getSession(sessionId: string): Promise<ChatSession | null> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction([ChatStorage.STORE_NAME], 'readonly');
      const store = transaction.objectStore(ChatStorage.STORE_NAME);

      return new Promise((resolve, reject) => {
        const request = store.get(sessionId);
        request.onsuccess = () => {
          resolve(request.result || null);
        };
        request.onerror = () => {
          reject(new Error(`获取会话失败: ${request.error?.message}`));
        };
      });
    } catch (error) {
      console.error('获取会话失败:', error);
      return null;
    }
  }

  /**
   * 清空所有会话数据
   */
  public async clearAllSessions(): Promise<void> {
    try {
      const db = await this.initDB();
      const transaction = db.transaction([ChatStorage.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(ChatStorage.STORE_NAME);

      await new Promise<void>((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error(`清空会话失败: ${request.error?.message}`));
      });
    } catch (error) {
      console.error('清空会话失败:', error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  public closeDB(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 导出单例实例
export const chatStorage = ChatStorage.getInstance();
