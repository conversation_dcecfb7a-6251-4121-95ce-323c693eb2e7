'use client'

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { LLMProvider } from "@/contexts/LLMContext";
import { QueryProvider } from "@/contexts/QueryContext";
import { CompletionProvider } from "@/contexts/CompletionContext";
import { EditorProvider } from "@/contexts/EditorContext";
import { ThemeProvider } from '@/contexts/ThemeContext';
import { ChatSettingsProvider } from '@/contexts/ChatSettingsContext';
import NavBar from "@/components/NavBar/NavBar";
import Loading from './loading';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});


export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();

  // 当路径变化时，显示加载状态
  useEffect(() => {
    setIsLoading(true);
    
    // 使用短暂的延迟来确保加载状态可见
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100); // 稍微延长加载时间，让用户能看到骨架屏效果
    
    return () => clearTimeout(timer);
  }, [pathname]);

  return (
    <html lang="zh-CN" className={`${geistSans.variable} ${geistMono.variable}`}>
      <head>
        <title>chatSQL</title>
        <meta name="description" content="Generated by create next app" />
      </head>
      <body>
        <ThemeProvider>
          <ChatSettingsProvider>
            <LLMProvider>
              <QueryProvider>
                <EditorProvider>
                  <CompletionProvider>
                    <NavBar />
                    {isLoading ? <Loading /> : children}
                  </CompletionProvider>
                </EditorProvider>
              </QueryProvider>
            </LLMProvider>
          </ChatSettingsProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
