'use client';

// 新聊天系统测试页面

import React from 'react';
import { Container, Typography, Box, Paper } from '@mui/material';
import ChatExample from '@/components/ChatBot/ChatExample';

const ChatTestPage: React.FC = () => {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'var(--primary-text)' }}>
          新聊天系统测试
        </Typography>
        <Typography variant="body1" sx={{ color: 'var(--secondary-text)', mb: 2 }}>
          这是新的基于Context的聊天状态和持久化管理系统的测试页面。
        </Typography>
        
        <Paper sx={{ p: 3, mb: 3, backgroundColor: 'var(--card-bg)' }}>
          <Typography variant="h6" gutterBottom sx={{ color: 'var(--primary-text)' }}>
            功能特性
          </Typography>
          <Box component="ul" sx={{ color: 'var(--secondary-text)' }}>
            <li>✅ 基于IndexedDB的持久化存储</li>
            <li>✅ 统一的Context状态管理</li>
            <li>✅ 会话创建和选择</li>
            <li>✅ 消息发送和显示</li>
            <li>✅ 会话删除功能</li>
            <li>✅ 错误处理和加载状态</li>
            <li>✅ TypeScript类型安全</li>
            <li>✅ 响应式设计</li>
          </Box>
        </Paper>

        <Paper sx={{ p: 3, mb: 3, backgroundColor: 'var(--card-bg)' }}>
          <Typography variant="h6" gutterBottom sx={{ color: 'var(--primary-text)' }}>
            使用说明
          </Typography>
          <Box component="ol" sx={{ color: 'var(--secondary-text)' }}>
            <li>点击左侧的 "+" 按钮创建新会话</li>
            <li>在会话列表中选择要查看的会话</li>
            <li>在右侧输入框中输入消息并发送</li>
            <li>消息会自动保存到IndexedDB中</li>
            <li>刷新页面后数据会自动恢复</li>
            <li>可以删除不需要的会话</li>
          </Box>
        </Paper>
      </Box>

      <ChatExample />
    </Container>
  );
};

export default ChatTestPage;
