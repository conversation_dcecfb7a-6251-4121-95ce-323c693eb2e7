// 新的聊天窗口组件 - 使用重构后的ChatContext

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Paper,
  Box,
  Typography,
  IconButton,
  Slide,
  Tooltip,
  Divider,
  Alert,
} from '@mui/material';
import {
  Close as CloseIcon,
  Minimize as MinimizeIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  SmartToy as AIIcon,
  Add as AddIcon,
  History as HistoryIcon,
} from '@mui/icons-material';
import { useChatContext } from '@/contexts/ChatContext';
import { useChatSettings } from '@/contexts/ChatSettingsContext';
import { ChatMessage } from '@/types/chat';
import { Message } from '@/types/chatbot';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import SettingsModal from './SettingsModal';
import styles from './ChatWindow.module.css';

interface NewChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  position?: { x: number; y: number };
  size?: { width: number; height: number };
}

/**
 * 将ChatMessage转换为MessageList组件期望的Message格式
 */
const convertChatMessagesToMessages = (chatMessages: ChatMessage[]): Message[] => {
  return chatMessages.map(msg => ({
    id: msg.id,
    content: msg.content,
    sender: msg.role === 'user' ? 'user' : 'ai',
    timestamp: msg.timestamp,
    metadata: msg.metadata && msg.metadata.module ? {
      module: msg.metadata.module,
      topic: msg.metadata.topic,
      action: msg.metadata.action
    } : undefined
  }));
};

/**
 * 新的聊天窗口组件 - 集成重构后的聊天系统
 */
const NewChatWindow: React.FC<NewChatWindowProps> = ({
  isOpen,
  onClose,
  position = { x: 16, y: 16 },
  size = { width: 400, height: 600 },
}) => {
  // 状态管理
  const [currentPosition, setCurrentPosition] = useState(position);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [showSessionList, setShowSessionList] = useState(false);

  // 使用新的ChatContext
  const {
    sessions,
    currentSessionId,
    messages,
    isLoading,
    error,
    selectSession,
    createNewSession,
    sendMessage,
    deleteSession,
    clearError,
  } = useChatContext();

  const {
    settings,
    saveSettings,
    updateWindowSize,
    getWindowSize,
  } = useChatSettings();

  // 从设置中获取窗口大小
  const [currentSize, setCurrentSize] = useState(() => getWindowSize());

  // Refs
  const windowRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  // 当窗口大小变化时，保存到设置中
  useEffect(() => {
    updateWindowSize(currentSize);
  }, [currentSize, updateWindowSize]);

  // 拖拽功能
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isFullscreen) return;
    
    const rect = windowRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
      setIsDragging(true);
    }
  }, [isFullscreen]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || isFullscreen || typeof window === 'undefined') return;

    const newX = e.clientX - dragOffset.x;
    const newY = e.clientY - dragOffset.y;

    // 限制窗口不超出屏幕边界
    const maxX = window.innerWidth - currentSize.width;
    const maxY = window.innerHeight - currentSize.height;

    setCurrentPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY)),
    });
  }, [isDragging, dragOffset, currentSize, isFullscreen]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  // 监听鼠标移动和释放事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 处理新建对话
  const handleNewChat = async () => {
    try {
      await createNewSession();
      setShowSessionList(false);
    } catch (error) {
      console.error('创建新会话失败:', error);
    }
  };

  // 处理选择会话
  const handleSelectSession = async (sessionId: string) => {
    try {
      await selectSession(sessionId);
      setShowSessionList(false);
    } catch (error) {
      console.error('选择会话失败:', error);
    }
  };

  // 切换最小化
  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 计算窗口样式
  const getWindowStyle = () => {
    if (isFullscreen) {
      return {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 1300,
      };
    }

    // 服务器端渲染时使用默认值
    if (typeof window === 'undefined') {
      return {
        position: 'fixed' as const,
        bottom: 16,
        right: 16,
        width: currentSize.width,
        height: isMinimized ? 'auto' : currentSize.height,
        zIndex: 1300,
      };
    }

    return {
      position: 'fixed' as const,
      bottom: window.innerHeight - currentPosition.y - currentSize.height,
      right: window.innerWidth - currentPosition.x - currentSize.width,
      width: currentSize.width,
      height: isMinimized ? 'auto' : currentSize.height,
      zIndex: 1300,
    };
  };

  // 获取当前会话信息
  const currentSession = currentSessionId ? sessions.find(s => s.id === currentSessionId) : null;

  return (
    <>
      <Slide direction="up" in={isOpen} mountOnEnter unmountOnExit>
        <Paper
          ref={windowRef}
          elevation={8}
          className={`${styles.window} ${isFullscreen ? styles.fullscreen : ''} ${isMinimized ? styles.minimized : ''} ${isDragging ? styles.dragging : ''}`}
          sx={{
            ...getWindowStyle(),
            backgroundColor: 'var(--card-bg)',
            border: '1px solid var(--card-border)',
            borderRadius: isFullscreen ? 0 : 2,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* 标题栏 */}
          <Box
            ref={headerRef}
            onMouseDown={handleMouseDown}
            className={`${styles.header} ${isFullscreen ? styles.fullscreen : ''}`}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              p: 1,
              backgroundColor: 'var(--sidebar-bg)',
              borderBottom: '1px solid var(--sidebar-border)',
              cursor: isFullscreen ? 'default' : 'grab',
              '&:active': {
                cursor: isFullscreen ? 'default' : 'grabbing',
              },
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AIIcon sx={{ color: 'primary.main', fontSize: 20 }} />
              <Typography
                variant="subtitle2"
                sx={{ color: 'var(--primary-text)', fontWeight: 'bold' }}
              >
                智能助手
              </Typography>
              {currentSession && (
                <Typography
                  variant="caption"
                  sx={{ color: 'var(--secondary-text)' }}
                >
                  {currentSession.title} • {currentSession.messageCount} 条消息
                </Typography>
              )}
              {isLoading && (
                <Typography
                  variant="caption"
                  sx={{ color: 'var(--secondary-text)' }}
                >
                  正在思考...
                </Typography>
              )}
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="新建对话">
                <IconButton
                  size="small"
                  onClick={handleNewChat}
                  disabled={isLoading}
                  sx={{ color: 'var(--icon-color)' }}
                >
                  <AddIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              <Tooltip title="会话历史">
                <IconButton
                  size="small"
                  onClick={() => setShowSessionList(!showSessionList)}
                  sx={{ color: 'var(--icon-color)' }}
                >
                  <HistoryIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              {!isFullscreen && (
                <Tooltip title={isMinimized ? '展开' : '最小化'}>
                  <IconButton
                    size="small"
                    onClick={toggleMinimize}
                    sx={{ color: 'var(--icon-color)' }}
                  >
                    <MinimizeIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}

              <Tooltip title={isFullscreen ? '退出全屏' : '全屏'}>
                <IconButton
                  size="small"
                  onClick={toggleFullscreen}
                  sx={{ color: 'var(--icon-color)' }}
                >
                  {isFullscreen ? (
                    <FullscreenExitIcon fontSize="small" />
                  ) : (
                    <FullscreenIcon fontSize="small" />
                  )}
                </IconButton>
              </Tooltip>

              <Tooltip title="关闭">
                <IconButton
                  size="small"
                  onClick={onClose}
                  sx={{ color: 'var(--icon-color)' }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* 错误提示 */}
          {error && (
            <Alert 
              severity="error" 
              onClose={clearError}
              sx={{ m: 1 }}
            >
              {error}
            </Alert>
          )}

          {/* 会话列表 */}
          {showSessionList && !isMinimized && (
            <Box sx={{ maxHeight: 200, overflow: 'auto', borderBottom: '1px solid var(--sidebar-border)' }}>
              {sessions.map((session) => (
                <Box
                  key={session.id}
                  onClick={() => handleSelectSession(session.id)}
                  sx={{
                    p: 1,
                    cursor: 'pointer',
                    backgroundColor: session.id === currentSessionId ? 'action.selected' : 'transparent',
                    '&:hover': { backgroundColor: 'action.hover' },
                    borderBottom: '1px solid var(--card-border)',
                  }}
                >
                  <Typography variant="body2" sx={{ color: 'var(--primary-text)' }} noWrap>
                    {session.title}
                  </Typography>
                  <Typography variant="caption" sx={{ color: 'var(--secondary-text)' }}>
                    {session.messageCount} 条消息 • {session.session_id ? 'API会话' : '新会话'}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}

          {/* 聊天内容 */}
          {!isMinimized && (
            <>
              <MessageList
                messages={convertChatMessagesToMessages(messages)}
                isLoading={isLoading}
                onActionConfirm={() => {}} // 可以根据需要实现
              />

              <MessageInput
                onSendMessage={sendMessage}
                disabled={isLoading}
                placeholder={currentSessionId ? "请输入您的问题..." : "请先创建或选择一个会话"}
              />
            </>
          )}
        </Paper>
      </Slide>

      {/* 设置Modal */}
      <SettingsModal
        open={isSettingsModalOpen}
        onClose={() => setIsSettingsModalOpen(false)}
        settings={settings}
        onSave={saveSettings}
      />
    </>
  );
};

export default NewChatWindow;
