/* ChatWindow组件样式模块 */

.chatWindow {
  /* 默认窗口尺寸 */
  --default-width: 400px;
  --default-height: 600px;
  --min-width: 300px;
  --min-height: 400px;
  --max-width: 800px;
  --max-height: 90vh;
  
  /* 颜色变量 */
  --window-bg: var(--card-bg);
  --window-border: var(--card-border);
  --header-bg: var(--sidebar-bg);
  --header-border: var(--sidebar-border);
  --text-primary: var(--primary-text);
  --text-secondary: var(--secondary-text);
  --icon-color: var(--icon-color);
  
  /* 阴影和圆角 */
  --window-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --window-radius: 8px;
  
  /* 动画时长 */
  --transition-duration: 0.2s;
}

/* 主窗口容器 */
.window {
  position: fixed;
  width: var(--default-width);
  height: var(--default-height);
  min-width: var(--min-width);
  min-height: var(--min-height);
  max-width: var(--max-width);
  max-height: var(--max-height);
  background-color: var(--window-bg);
  border: 1px solid var(--window-border);
  border-radius: var(--window-radius);
  box-shadow: var(--window-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: row;
  z-index: 1300;
  transition: all var(--transition-duration) ease-in-out;
}

/* 全屏模式 */
.window.fullscreen {
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0;
  max-width: none;
  max-height: none;
}

/* 最小化模式 */
.window.minimized {
  height: auto !important;
}

/* 拖拽状态 */
.window.dragging {
  cursor: grabbing;
  user-select: none;
  transition: none;
}

/* 调整大小状态 */
.window.resizing {
  transition: none;
}

/* 主内容区域 */
.mainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 防止flex子元素溢出 */
}

/* 标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  cursor: grab;
  user-select: none;
}

.header:active {
  cursor: grabbing;
}

.header.fullscreen {
  cursor: default;
}

.header.fullscreen:active {
  cursor: default;
}

/* 标题区域 */
.titleSection {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.titleIcon {
  color: var(--primary-main);
  font-size: 20px;
  flex-shrink: 0;
}

.titleText {
  color: var(--text-primary);
  font-weight: bold;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.statusText {
  color: var(--text-secondary);
  font-size: 12px;
  margin-left: 8px;
  white-space: nowrap;
}

/* 控制按钮区域 */
.controls {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.controlButton {
  color: var(--icon-color);
  padding: 4px;
  border-radius: 4px;
  transition: background-color var(--transition-duration) ease;
}

.controlButton:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

/* 聊天内容区域 */
.chatContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 调整大小手柄 */
.resizeHandle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: nw-resize;
  background-color: transparent;
  transition: background-color var(--transition-duration) ease;
}

.resizeHandle:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.resizeHandle::after {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-bottom: 8px solid var(--icon-color);
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .window {
    --default-width: 100vw;
    --default-height: 100vh;
    --min-width: 100vw;
    --min-height: 100vh;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    border-radius: 0;
  }
  
  .header {
    padding: 12px 16px;
  }
  
  .titleText {
    font-size: 16px;
  }
}

/* 动画效果 */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

.window.entering {
  animation: slideUp var(--transition-duration) ease-out;
}

.window.exiting {
  animation: slideDown var(--transition-duration) ease-in;
}

/* 加载状态 */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 错误状态 */
.error {
  border-color: var(--error-color, #f44336);
}

/* 焦点状态 */
.window:focus-within {
  box-shadow: var(--window-shadow), 0 0 0 2px var(--primary-main, #1976d2);
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .chatWindow {
    --window-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  .controlButton:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
  
  .resizeHandle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
