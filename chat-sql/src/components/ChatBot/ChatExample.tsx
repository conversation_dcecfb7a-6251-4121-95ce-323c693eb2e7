// 新聊天系统使用示例组件

import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  IconButton,
  TextField,
  Alert,
  Chip,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Chat as ChatIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useChatContext } from '@/contexts/ChatContext';
import { formatTimestamp } from '@/types/chat';

/**
 * 聊天系统使用示例组件
 */
const ChatExample: React.FC = () => {
  const {
    sessions,
    currentSessionId,
    messages,
    isLoading,
    error,
    selectSession,
    createNewSession,
    sendMessage,
    deleteSession,
    clearError,
    refreshSessions,
  } = useChatContext();

  const [inputMessage, setInputMessage] = useState('');

  // 处理发送消息
  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;
    
    try {
      await sendMessage(inputMessage);
      setInputMessage('');
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  // 处理创建新会话
  const handleCreateSession = async () => {
    try {
      await createNewSession();
    } catch (error) {
      console.error('创建会话失败:', error);
    }
  };

  // 处理删除会话
  const handleDeleteSession = async (sessionId: string) => {
    if (window.confirm('确定要删除这个会话吗？')) {
      try {
        await deleteSession(sessionId);
      } catch (error) {
        console.error('删除会话失败:', error);
      }
    }
  };

  return (
    <Box sx={{ display: 'flex', height: '600px', gap: 2 }}>
      {/* 左侧：会话列表 */}
      <Paper sx={{ width: 300, p: 2, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" sx={{ color: 'var(--primary-text)' }}>
            会话列表
          </Typography>
          <Box>
            <IconButton 
              size="small" 
              onClick={refreshSessions}
              disabled={isLoading}
              title="刷新会话列表"
            >
              <RefreshIcon />
            </IconButton>
            <IconButton 
              size="small" 
              onClick={handleCreateSession}
              disabled={isLoading}
              title="创建新会话"
            >
              <AddIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* 会话列表 */}
        <List sx={{ flex: 1, overflow: 'auto' }}>
          {sessions.map((session) => (
            <ListItem
              key={session.id}
              disablePadding
              secondaryAction={
                <IconButton
                  edge="end"
                  size="small"
                  onClick={() => handleDeleteSession(session.id)}
                  disabled={isLoading}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              }
            >
              <ListItemButton
                selected={session.id === currentSessionId}
                onClick={() => selectSession(session.id)}
                disabled={isLoading}
              >
                <ListItemText
                  primary={session.title}
                  secondary={`${formatTimestamp(session.updatedAt)} • ${session.messages.length} 条消息`}
                  primaryTypographyProps={{
                    sx: { color: 'var(--primary-text)' },
                    noWrap: true
                  }}
                  secondaryTypographyProps={{
                    sx: { color: 'var(--secondary-text)' },
                    fontSize: '0.75rem'
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
          {sessions.length === 0 && (
            <ListItem>
              <ListItemText
                primary="暂无会话"
                secondary="点击 + 按钮创建新会话"
                primaryTypographyProps={{ sx: { color: 'var(--secondary-text)', textAlign: 'center' } }}
                secondaryTypographyProps={{ sx: { color: 'var(--secondary-text)', textAlign: 'center' } }}
              />
            </ListItem>
          )}
        </List>

        {/* 状态信息 */}
        <Box sx={{ mt: 2 }}>
          <Chip
            icon={<ChatIcon />}
            label={`${sessions.length} 个会话`}
            size="small"
            variant="outlined"
          />
          {isLoading && (
            <Chip
              label="加载中..."
              size="small"
              color="primary"
              sx={{ ml: 1 }}
            />
          )}
        </Box>
      </Paper>

      {/* 右侧：聊天区域 */}
      <Paper sx={{ flex: 1, p: 2, display: 'flex', flexDirection: 'column' }}>
        {/* 错误提示 */}
        {error && (
          <Alert 
            severity="error" 
            onClose={clearError}
            sx={{ mb: 2 }}
          >
            {error}
          </Alert>
        )}

        {/* 聊天标题 */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ color: 'var(--primary-text)' }}>
            {currentSessionId ? 
              sessions.find(s => s.id === currentSessionId)?.title || '聊天窗口' : 
              '请选择或创建一个会话'
            }
          </Typography>
          {currentSessionId && (
            <Typography variant="caption" sx={{ color: 'var(--secondary-text)' }}>
              会话ID: {currentSessionId}
            </Typography>
          )}
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* 消息列表 */}
        <Box sx={{ flex: 1, overflow: 'auto', mb: 2 }}>
          {messages.length === 0 ? (
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center', 
              height: '100%',
              color: 'var(--secondary-text)'
            }}>
              <Typography>
                {currentSessionId ? '开始新的对话吧！' : '请先选择或创建一个会话'}
              </Typography>
            </Box>
          ) : (
            <List>
              {messages.map((message) => (
                <ListItem key={message.id} sx={{ flexDirection: 'column', alignItems: 'stretch' }}>
                  <Paper
                    sx={{
                      p: 2,
                      backgroundColor: message.role === 'user' ? 'primary.light' : 'grey.100',
                      color: message.role === 'user' ? 'primary.contrastText' : 'text.primary',
                      alignSelf: message.role === 'user' ? 'flex-end' : 'flex-start',
                      maxWidth: '80%',
                    }}
                  >
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      {message.content}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.7 }}>
                      {message.role === 'user' ? '您' : 'AI'} • {formatTimestamp(message.timestamp)}
                    </Typography>
                  </Paper>
                </ListItem>
              ))}
            </List>
          )}
        </Box>

        {/* 输入区域 */}
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={currentSessionId ? "输入消息..." : "请先选择或创建一个会话"}
            disabled={!currentSessionId || isLoading}
            variant="outlined"
            size="small"
          />
          <Button
            variant="contained"
            onClick={handleSendMessage}
            disabled={!currentSessionId || !inputMessage.trim() || isLoading}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            <SendIcon />
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default ChatExample;
